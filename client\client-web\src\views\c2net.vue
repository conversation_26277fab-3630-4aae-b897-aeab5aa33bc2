<template>
  <div>
    <div class="background">
      <el-container>
        <el-header>
          <el-row justify="center" type="flex">
            <el-col :span="12">
              <div class="title">中国算力网实验场（仿真）</div>
            </el-col>
          </el-row>
        </el-header>

        <el-main>
          <el-row justify="space-around" type="flex">
            <!-- 左侧栏 - 仿真任务总览和列表 -->
            <el-col :span="7">
              <div class="showMt">
                <el-row class="linear-gradient" style="margin-bottom: 3px">
                  <el-col :span="14">
                    <el-row>
                      <el-col :span="5">
                        <div class="arrow"></div>
                      </el-col>
                      <el-col :span="19">
                        <div class="title1">仿真任务总览</div>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
                <el-row justify="space-between" type="flex">
                  <el-col :span="24">
                    <div>
                      <CumulativeTask :Data="Data" :msg="msg2"></CumulativeTask>
                    </div>
                    <div>
                      <div
                        key="demo1"
                        ref="echart"
                        style="width: 100%; height: 250px"
                      ></div>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div class="showMt">
                <el-row class="linear-gradient">
                  <el-col :span="19">
                    <el-row align="middle" type="flex">
                      <el-col :span="3">
                        <div class="arrow"></div>
                      </el-col>
                      <el-col :span="16">
                        <div class="title1">仿真任务列表</div>
                      </el-col>
                    </el-row>
                  </el-col>
                  <el-col :span="5" style="text-align: right; padding-right: 15px;">
                    <div class="task-buttons-container">
                      <el-button
                        size="small"
                        type="primary"
                        @click="openAddTaskDialog"
                        style="margin-right: 8px; position: relative; z-index: 1000;"
                      >新增任务
                      </el-button>
                      <el-button
                        size="small"
                        type="success"
                        @click="openAddCooperativeTaskDialog"
                        style="position: relative; z-index: 1000;"

                      >新增协同任务
                      </el-button>
                    </div>
                  </el-col>
                </el-row>
                <Table
                  ref="table"
                  :Count="count"
                  :Stop="Stop"
                  :Total="total"
                  class="index"
                  @row-click="openTaskDetailDialog"
                ></Table>
              </div>
            </el-col>
            <el-col :span="14">
              <div>
                <el-row justify="center" type="flex">
                  <el-col :span="18">
                    <Total :msg="msg" :taskDetail="taskDetail"></Total>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <div style="max-height: 800px; overflow: hidden">
                      <chinaMap ref="chinaMap"
                                :Count="count"
                                :Stop="Stop"
                                :intervalSetting="interval"
                                :taskResponse="taskIdRes"
                      ></chinaMap>
                      <div class="progress">
                        <div style="width: 600px; margin: 0 auto">
                          <div
                            style="
                              display: inline-block;
                              min-width: 150px;
                              margin-right: 10px;
                            "
                          >
                            <el-slider
                              v-if="showPer && intervalChange"
                              v-model="percentage"
                              :disabled="disable"
                              :format-tooltip="formatTooltip"
                              class="progressStyle"
                              @change="changeProgress"
                            ></el-slider>
                          </div>
                          <div
                            v-if="intervalChange"
                            style="width: 80px; display: inline-block"
                          >
                            <el-button
                              :disabled="disable"
                              size="small"
                              @click="start"
                            >开始
                            </el-button
                            >
                          </div>
                          <div
                            v-show="buttonShow && showButtom"
                            style="width: 80px; display: inline-block"
                          >
                            <el-button
                              :disabled="disable"
                              size="small"
                              @click="stop"
                            >暂停
                            </el-button
                            >
                          </div>
                          <div
                            v-show="!buttonShow && showButtom"
                            style="width: 80px; display: inline-block"
                          >
                            <el-button
                              :disabled="disable"
                              size="small"
                              @click="goOn"
                            >继续
                            </el-button
                            >
                          </div>
                          <div style="display: inline-block">
                            <el-select
                              v-if="intervalChange"
                              v-model="interval"
                              :disabled="disable"
                              class="interval"
                              placeholder="请选择仿真时间间隔"
                              style="
                                width: 150px;
                                display: inline-block;
                                margin-left: 10px;
                              "
                              @change="changeInterval"
                            >
                              <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              >
                              </el-option>
                            </el-select>
                          </div>
                        </div>
                      </div>
                      <!-- <dv-loading v-if="!show" class="loading"
                        ><span class="loading-text">Loading</span></dv-loading
                      > -->
                    </div>
                    <!-- <div class="statusWrapper" v-if="show1 || show3">
                      <div class="wrapper">
                        <div
                          :class="{
                            buttonStyle2: true,
                            check2: type1,
                            title2: true,
                          }"
                          @click="type = 1"
                        >
                          <span class="img1 icon"></span
                          ><span class="text">智算中心</span>
                          <span class="text">{{ intelligenceTotal }}</span>
                        </div>
                        <div
                          :class="{
                            buttonStyle2: true,
                            check2: type2,
                            title2: true,
                          }"
                          @click="type = 2"
                        >
                          <span class="img2 icon"></span
                          ><span class="text">超算中心</span>
                          <span class="text">{{ superTotal }}</span>
                        </div>
                        <div
                          :class="{
                            buttonStyle2: true,
                            check2: type3,
                            title2: true,
                          }"
                          @click="type = 3"
                        >
                          <span class="img3 icon"></span
                          ><span class="text">东数西算</span>
                          <span class="text">{{ eAdnwTotal }}</span>
                        </div>
                        <div class="title3" @click="type = 3">
                          <span class="img4 icon2"></span>
                          <span class="text">已接入</span>
                          <span class="text">{{ statusData.connected }}</span>
                        </div>
                        <div class="title3" @click="type = 3">
                          <span class="img5 icon2"></span>
                          <span class="text">接入中</span>
                          <span class="text">{{ statusData.accessing }}</span>
                        </div>
                        <div class="title3" @click="type = 3">
                          <span class="img6 icon2"></span>
                          <span class="text">待接入</span>
                          <span class="text">{{ statusData.pending }}</span>
                        </div>
                      </div>
                    </div> -->
                    <!-- <div class="statusWrapper2" v-if="show2">
                      <div class="wrapper">
                        <div class="arrow1">
                          <span class="ml-2">10TB全光网络互联</span>
                        </div>
                        <div class="arrow2">
                          <span class="ml-2">SD-WAN互联</span>
                        </div>
                        <div class="arrow3">
                          <span class="ml-2">MPLS互联</span>
                        </div>
                      </div>
                    </div> -->
                    <!-- <provinceMap
                      v-if="show1"
                      style="position: absolute; top: 0px"
                    ></provinceMap> -->
                    <provinceMap2
                      v-if="show2"
                      style="position: absolute; top: 0px"
                    ></provinceMap2>
                    <provinceMap3
                      v-if="show3"
                      :Data="tipData"
                      style="position: absolute; top: 0px"
                    ></provinceMap3>
                    <el-row
                      :gutter="30"
                      justify="center"
                      style="position: relative; top: 0px; left: 70px"
                      type="flex"
                    >
                      <!-- <el-col :span="7">
                        <div
                          :class="{ buttonStyle: true, check: show1 }"
                          @click="changeType()"
                        >
                          仿真算力集群
                        </div>
                      </el-col>
                      <el-col :span="7">
                        <div
                          :class="{ buttonStyle: true, check: show2 }"
                          @click="changeType()"
                        >
                          仿真调度任务
                        </div>
                      </el-col> -->
                    </el-row>
                  </el-col>
                </el-row>
              </div>
            </el-col>
            <el-col :span="5">
              <!-- <div class="taskMsg">
                <div class="taskName">任务ID</div>
                <div class="taskValue">{{ taskDetail.ID }}</div>
              </div> -->
              <div class="taskDetailWrapper">
                <!--                <div>-->
                <!--                  <TASKDETAIL-->
                <!--                    :taskDetail="taskDetail"-->
                <!--                    :runningTime="runningTime"-->
                <!--                  ></TASKDETAIL>-->
                <!--                </div>-->
                <div>
                  <div class="mt">
                    <el-row justify="center" type="flex">
                      <el-col
                      >
                        <div>
                          <el-row
                            class="row-bg"
                            justify="space-around"
                            type="flex"
                            :gutter="16"
                          >
                            <el-col :span="8" class="bg2" :class="{'active-tab': check1}">
                              <el-link
                                :class="{ button2: true, check3: check1 }"
                                :disabled="disable"
                                :underline="false"
                                @click="change(1)"
                              >提交任务量
                              </el-link
                              >
                            </el-col>
                            <el-col :span="8" class="bg2" :class="{'active-tab': check2}">
                              <el-link
                                :class="{ button2: true, check3: check2 }"
                                :disabled="disable"
                                :underline="false"
                                @click="change(2)"
                              >
                                任务平均等待时长
                              </el-link
                              >
                            </el-col>
                            <el-col :span="8" class="bg2" :class="{'active-tab': check3}">
                              <el-link
                                :class="{ button2: true, check3: check3 }"
                                :disabled="disable"
                                :underline="false"
                                @click="change(3)"
                              >资源利用率对比
                              </el-link
                              >
                            </el-col>
                          </el-row>
                        </div>
                      </el-col
                      >
                    </el-row>
                  </div>
                  <div class="chart-scroll-container" :style="chartContainerStyle">
                    <div
                      key="demo2"
                      ref="echart2"
                      style="width: 100%; height: 100%;"
                    ></div>
                  </div>
                </div>
              </div>

              <!-- <div class="showMt">
                <el-row class="linear-gradient" style="margin-
                m: 7px">
                  <el-col :span="2">
                    <div class="arrow"></div>
                  </el-col>
                  <el-col :span="20">
                    <div class="title1">算力使用趋势</div>
                  </el-col>
                </el-row>
                <TEND id="TEND" :config="tendConfig"></TEND>
              </div>
              <div class="showMt">
                <el-row class="linear-gradient">
                  <el-col :span="2">
                    <div class="arrow"></div>
                  </el-col>
                  <el-col :span="20">
                    <div class="title1">各中心算力使用情况</div>
                  </el-col>
                </el-row>
                <STATUS
                  id="Status"
                  :data="centerData"
                  :config="statusConfig"
                ></STATUS>
              </div>
              <div class="showMt">
                <el-row class="linear-gradient" style="margin-bottom: 20px">
                  <el-col :span="2">
                    <div class="arrow"></div>
                  </el-col>
                  <el-col :span="20">
                    <div class="title1">算力接入情况</div>
                  </el-col>
                </el-row>
                <INSERT
                  id="Imsert"
                  :data="connectedData"
                  :config="connectedConfig"
                ></INSERT>
              </div> -->
            </el-col>
          </el-row>
        </el-main>
        <el-row justify="center" type="flex">
          <!-- <div class="coopera-pic"></div> -->
        </el-row>
      </el-container>
      <div class="footer"></div>
      <!-- <el-footer class="footer"></el-footer> -->
    </div>
    <!-- <div class="loading" v-if="stopClick">
      <dv-loading>Loading...</dv-loading>
    </div> -->
    <!-- 使用组件化的任务弹窗 -->
    <TaskDialog
      :visible.sync="addTaskDialogVisible"
      @close="handleCloseTaskDialog"
      @submit="submitTaskForm"
    />

    <!-- 协同任务弹窗 -->
    <CooperativeTaskDialog
      :visible.sync="addCooperativeTaskDialogVisible"
      @close="handleCloseCooperativeTaskDialog"
      @submit="submitCooperativeTaskForm"
    />

    <!-- 普通任务详情弹窗 -->
    <TaskDetailDialog
      v-if="!currentTaskIsCooperative"
      :visible.sync="taskDetailDialogVisible"
      :taskData="currentTaskData"
      @close="handleTaskDetailClose"
      @submit-success="handleTaskSubmitSuccess"
    />

    <!-- 协同任务详情弹窗 -->
    <CooperativeTaskDetailDialog
      v-if="currentTaskIsCooperative"
      :visible.sync="taskDetailDialogVisible"
      :taskData="currentTaskData"
      @close="handleTaskDetailClose"
      @submit-success="handleTaskSubmitSuccess"
    />
  </div>

</template>
<script>
// 保留使用的组件导入
import TEND from '@/components/lineChart'
import INSERT from '@/components/HisAndLine'
import Total from '@/components/Total'
import chinaMap from '@/components/chinaMapDemo'
import Table from '@/components/Table'
import provinceMap2 from '@/components/provinceMap5'
import provinceMap3 from '@/components/provinceMap3'
import circle from '@/components/circle'
import TaskDialog from '@/components/TaskDialog'
import CooperativeTaskDialog from '@/components/CooperativeTaskDialog'
import { jobDetail } from '@/api/screenService.js'
import { loadTaskYaml } from '@/api/yamlService'
import TaskDetailDialog from '@/components/TaskDetailDialog'
import CooperativeTaskDetailDialog from '@/components/CooperativeTaskDetailDialog'
import MultiTaskService from '@/services/MultiTaskService'

// 导入拆分的模块
import dataManager from './c2net/dataManager'
import taskProcessor from './c2net/taskProcessor'
import animationController from './c2net/animationController'
import chartConfig from './c2net/chartConfig'
import barSeriesConfig from './c2net/barSeriesConfig'
import dialogManager from './c2net/dialogManager'
import utils from './c2net/utils'

export default {
  components: {
    TEND,
    INSERT,
    Total,
    chinaMap,
    provinceMap2,
    provinceMap3,
    CumulativeTask: circle,
    Table,
    TaskDialog,
    CooperativeTaskDialog,
    TaskDetailDialog,
    CooperativeTaskDetailDialog
  },
  data () {
    return {
      // 对话框相关状态
      addTaskDialogVisible: false,
      addCooperativeTaskDialogVisible: false,
      taskDetailDialogVisible: false,
      currentTaskId: '',
      currentTaskIsCooperative: false, // 新增：标识当前任务是否为协同任务
      currentTaskData: null, // 新增：存储当前任务的详细数据

      // 图表数据
      taskData: {
        xData: [],
        used: [],
        used2: [],
        // 新增：支持多个任务的数据数组
        multiTaskData: [] // 格式: [{taskId, strategy, submitData, pendingData, resourceData}, ...]
      },
      Data: {
        xAxis: [],
        yAxis: []
      },

      // 任务状态信息
      msg: {
        SnapshotTime: 0,
        NCenters: 0,
        NPops: 0
      },
      msg2: {
        totalNum: 0,
        execNum: 0
      },
      taskDetail: {
        ID: undefined,
        NJobs: undefined,
        SnapshotTime: undefined,
        CompletedFlag: undefined,
        strategy: undefined
      },

      // 任务对比数据
      submitJobCompare0: null,
      pendingJobCompare0: null,
      resouceCompare0: null,
      submitJobCompare: null,
      pendingJobCompare: null,
      resouceCompare: null,

      // 计时和控制相关
      timer: null,
      count: 0,
      nowCount: 0,
      total: 0,
      total2: 0,
      runningTime: 0,

      // 图表和任务类型
      check1: true,
      check2: false,
      check3: false,
      taskType: 1,
      name: '提交任务量',
      myChart: null,
      myChart2: null,

      // UI控制状态
      buttonShow: true,
      disable: false,
      Stop: true,
      showButtom: true,
      showPer: true,
      intervalChange: true,
      showStart: false,

      // 时间配置
      options: [
        { value: 24, label: '24h' },
        { value: 12, label: '12h' },
        { value: 6, label: '6h' }
      ],
      value: 24,
      percentage: 0,

      // 数据缓存
      temp: null,
      taskIdRes: null,
      compareIdRes: null,
      CenterInfoToWebList: null,
      dataAlreadyLoaded: false,

      // 地图相关
      show2: false,
      show3: false,
      tipData: {}
    }
  },
  created () {
    // this.drawLine()
    // this.taskCenter = centerMsg().points;
    // this.getJob();
  },
  computed: {
    taskId () {
      return this.$store.state.id
    },
    compareId () {
      return this.$store.state.compareId
    },
    Strategy1 () {
      return this.$store.state.strategy1
    },
    Strategy2 () {
      return this.$store.state.strategy2
    },
    lastTime () {
      return this.$store.state.lastTime
    },
    interval () {
      return this.$store.state.interval
    },
    // 新增：多任务相关的computed属性
    selectedTasks () {
      return this.$store.state.selectedTasks
    },
    selectedTaskIds () {
      return this.$store.state.selectedTaskIds
    },
    // 动态计算图表容器样式
    chartContainerStyle () {
      return {
        width: '100%',
        height: '700px', // 固定容器高度
        maxHeight: '700px',
        overflowY: 'auto', // 添加垂直滚动条
        overflowX: 'hidden' // 隐藏水平滚动条
      }
    },
    // 计算图表实际高度
    chartHeight () {
      const cityCount = this.taskData.xData ? this.taskData.xData.length : 0
      const taskCount = this.taskData.multiTaskData && this.taskData.multiTaskData.length > 0
        ? this.taskData.multiTaskData.length
        : (this.compareId != 0 && this.taskData.used2 && this.taskData.used2.length > 0 ? 2 : 1)

      return this.calculateChartHeight(cityCount, taskCount)
    }
    // percentage: {
    //   get() {
    //     if (this.count == 0 || this.total == 0) {
    //       return 0;
    //     } else {
    //       // console.log(this.count / this.total)
    //       return Number(((this.count / this.total) * 100).toFixed(0));
    //     }
    //   },
    //   set() {
    //     if (this.count == 0 || this.total == 0) {
    //       return 0;
    //     } else {
    //       return Number(((this.count / this.total) * 100).toFixed(0));
    //     }
    //   },
    // },
  },
  watch: {
    taskId (newValue) {
      // 保持地图状态
      if (this.$refs.chinaMap) {
        this.$refs.chinaMap.stopped = false;

        // 如果地图上有数据，保留它们
        if (this.$refs.chinaMap.nowConfig &&
          this.$refs.chinaMap.nowConfig.points &&
          this.$refs.chinaMap.nowConfig.points.length > 0) {
          // 地图数据已存在，不清空
        } else {
          // 如果没有数据，初始化演示数据
          this.$refs.chinaMap.initDemoData();
          this.$refs.chinaMap.showDot();
        }
      }

      // 重置数据
      this.$nextTick(() => {
        this.resetAllChartData();
        this.intervalChange = this.taskId !== 0;
      });
    },

    compareId () {
      // 保持地图状态
      if (this.$refs.chinaMap) {
        this.$refs.chinaMap.stopped = false;
      }

      // 重置图表数据
      this.$nextTick(() => {
        this.resetAllChartData();
      });
    },

    count (newValue) {
      // 更新进度百分比
      if (this.count == this.total) {
        this.percentage = 100;
      } else if (this.count == 0) {
        this.percentage = 0;
      } else {
        this.percentage = Number(((this.count / this.total) * 100).toFixed(0));
      }

      // 更新数据和状态
      if (newValue == this.total) {
        this.Data.yAxis = this.temp;
        this.drawLine();
        clearInterval(this.timer);
        this.percentage = 100;
        this.Stop = true;
      } else {
        // 更新折线图数据
        this.temp.forEach((item, key) => {
          if (key <= this.count + 1) {
            this.Data.yAxis[key] = this.temp[key];
          } else {
            this.Data.yAxis[key] = '';
          }
        });
      }
    }
  },
  mounted () {
    // 初始化图表
    this.$nextTick(() => {
      // 初始化图表实例
      if (this.myChart) {
        this.myChart.dispose();
      }
      if (this.myChart2) {
        this.myChart2.dispose();
      }

      this.myChart = this.$echarts.init(this.$refs.echart);
      this.myChart2 = this.$echarts.init(this.$refs.echart2);

      // 初始化空数据
      this.resetAllChartData();

      // 添加窗口大小调整监听
      window.addEventListener('resize', this.resize);
    });
  },
  methods: {
    // 混入拆分的模块方法
    ...dataManager,
    ...taskProcessor,
    ...animationController,
    ...chartConfig,
    ...barSeriesConfig,
    ...dialogManager,
    ...utils,

    // 新增任务相关方法
    openAddTaskDialog () {
      this.addTaskDialogVisible = true
    },
    handleCloseTaskDialog () {
      this.addTaskDialogVisible = false
    },
    // 新增协同任务相关方法
    openAddCooperativeTaskDialog () {
      this.addCooperativeTaskDialogVisible = true
    },
    handleCloseCooperativeTaskDialog () {
      this.addCooperativeTaskDialogVisible = false
    },
    submitTaskForm (responseOrFormData) {
      console.log('submitTaskForm被调用，参数:', responseOrFormData)

      // 判断参数是否为generateYaml API的响应
      if (responseOrFormData && (responseOrFormData.yaml_content || responseOrFormData.filename)) {
        // 如果是来自TaskDialog的generateYaml响应
        this.$message.success('任务创建成功')
        this.addTaskDialogVisible = false

        // 延迟1秒后刷新任务列表
        setTimeout(() => {
          console.log('新增任务成功后刷新任务列表')
          if (this.$refs.table) {
            console.log('调用table.init()刷新任务列表')
            this.$refs.table.init()
          } else {
            console.error('table引用不存在，无法刷新任务列表')
          }
        }, 1000)
      } else {
        // 如果是表单数据，执行原来的逻辑
        this.$http.post('/api/v1/tasks', responseOrFormData)
          .then(response => {
            this.$message.success('任务创建成功')
            this.addTaskDialogVisible = false
            // 延迟1秒后刷新任务列表
            setTimeout(() => {
              console.log('新增任务成功后刷新任务列表')
              if (this.$refs.table) {
                console.log('调用table.init()刷新任务列表')
                this.$refs.table.init()
              } else {
                console.error('table引用不存在，无法刷新任务列表')
              }
            }, 1000)
          })
          .catch(error => {
            this.$message.error('创建任务失败: ' + error.message)
          })
      }
    },
    submitCooperativeTaskForm (responseOrFormData) {
      console.log('submitCooperativeTaskForm被调用，参数:', responseOrFormData)

      // 判断参数是否为generateYaml API的响应
      if (responseOrFormData && (responseOrFormData.yaml_content || responseOrFormData.filename)) {
        // 如果是来自CooperativeTaskDialog的generateYaml响应
        this.$message.success('协同任务创建成功')
        this.addCooperativeTaskDialogVisible = false

        // 延迟1秒后刷新任务列表
        setTimeout(() => {
          console.log('新增协同任务成功后刷新任务列表')
          if (this.$refs.table) {
            console.log('调用table.init()刷新任务列表')
            this.$refs.table.init()
          } else {
            console.error('table引用不存在，无法刷新任务列表')
          }
        }, 1000)
      } else {
        // 如果是表单数据，执行原来的逻辑
        this.$http.post('/api/v1/cooperative-tasks', responseOrFormData)
          .then(response => {
            this.$message.success('协同任务创建成功')
            this.addCooperativeTaskDialogVisible = false
            // 延迟1秒后刷新任务列表
            setTimeout(() => {
              console.log('新增协同任务成功后刷新任务列表')
              if (this.$refs.table) {
                console.log('调用table.init()刷新任务列表')
                this.$refs.table.init()
              } else {
                console.error('table引用不存在，无法刷新任务列表')
              }
            }, 1000)
          })
          .catch(error => {
            this.$message.error('创建协同任务失败: ' + error.message)
          })
      }
    },
    // 打开任务详情弹窗
    openTaskDetailDialog (row) {
      console.log('打开任务详情', row)
      this.currentTaskId = row.ID

      // 通过API获取任务配置，判断是否为协同任务
      console.log('正在请求任务配置以判断任务类型...')

      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在加载任务信息...',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      loadTaskYaml(this.currentTaskId)
        .then(response => {
          console.log('获取任务配置成功:', response)

          // 根据配置内容判断是否为协同任务
          const config = response.yaml_data || {}

          // 判断标准：根据c2netJobConfig.useJsonFile字段
          // useJsonFile为false时是协同任务，为true或不存在时是普通任务
          let isCooperativeTask = false
          if (config.c2netJobConfig && config.c2netJobConfig.hasOwnProperty('useJsonFile')) {
            isCooperativeTask = (config.c2netJobConfig.useJsonFile === false)
          } else {
            // 如果没有useJsonFile字段，则根据其他特征判断（降级逻辑）
            isCooperativeTask = !!(
              config.c2netComputingCenterList ||
              (config.c2netJobConfig && config.c2netJobConfig.c2netJobGroupList) ||
              config.scheduleConfig ||
              (config.networkConfig && (config.networkConfig.construct || config.networkConfig.accelerator))
            )
          }

          this.currentTaskIsCooperative = isCooperativeTask
          // 将完整的数据存储到currentTaskData中，供弹窗组件使用
          this.currentTaskData = {
            taskId: this.currentTaskId,
            taskInfo: response.task || {},
            yamlData: config,
            rawResponse: response
          }
          console.log('任务类型判断结果:', {
            taskId: this.currentTaskId,
            isCooperativeTask,
            useJsonFile: config.c2netJobConfig?.useJsonFile,
            hasUseJsonFile: config.c2netJobConfig?.hasOwnProperty('useJsonFile'),
            configStructure: {
              hasC2netJobConfig: !!config.c2netJobConfig,
              hasComputingCenterList: !!config.c2netComputingCenterList,
              hasScheduleConfig: !!config.scheduleConfig,
              hasNetworkConfig: !!config.networkConfig
            }
          })

          // 确保DOM更新后再显示弹窗
          this.$nextTick(() => {
            this.taskDetailDialogVisible = true
            console.log('当前任务ID:', this.currentTaskId, '协同任务:', this.currentTaskIsCooperative, '对话框可见性:', this.taskDetailDialogVisible)
          })
        })
        .catch(error => {
          console.error('获取任务配置失败:', error)

          // 如果API请求失败，降级使用row中的数据（如果存在）
          if (row.hasOwnProperty('useJsonFile')) {
            console.log('API请求失败，降级使用row数据判断任务类型')
            this.currentTaskIsCooperative = row.useJsonFile === true || row.useJsonFile === 1
            console.log('降级判断结果 - 协同任务:', this.currentTaskIsCooperative)
          } else {
            // 默认当作普通任务处理
            console.log('无法判断任务类型，默认作为普通任务处理')
            this.currentTaskIsCooperative = false
          }

          // 确保DOM更新后再显示弹窗
          this.$nextTick(() => {
            this.taskDetailDialogVisible = true
          })
        })
        .finally(() => {
          loading.close()
        })
    },
    // 处理任务详情弹窗关闭
    handleTaskDetailClose () {
      this.taskDetailDialogVisible = false
      // 清空任务数据
      this.currentTaskData = null
      this.currentTaskId = ''
      this.currentTaskIsCooperative = false
    },
    // 处理任务提交成功
    handleTaskSubmitSuccess () {
      console.log('任务提交/重新提交成功事件被触发')
      // 刷新任务列表
      if (this.$refs.table) {
        console.log('调用table.init()刷新任务列表')
        this.$refs.table.init()
      } else {
        console.error('table引用不存在，无法刷新任务列表')
      }
    },
    // 基础数据操作方法

    /**
     * 重置图表和任务数据
     */
    resetChartData () {
      // 初始化为空数据，仅保持图表框架
      this.Data = {
        xAxis: [],
        yAxis: []
      }

      this.taskData = {
        xData: [],
        used: [],
        used2: [],
        multiTaskData: []
      }

      this.drawLine()
      this.drawLine2()
      this.count = 0
      this.buttonShow = true
      // 保持showButtom为true，以便显示暂停/继续按钮
      // this.showButtom = false
    },

    /**
     * 重置主要信息和任务详情
     */
    resetTaskInfo () {
      this.msg = {
        SnapshotTime: 0,
        NCenters: 0,
        NPops: 0
      }
      this.msg2 = {
        totalNum: 0,
        execNum: 0
      }
      this.taskDetail = {
        ID: undefined,
        NJobs: undefined,
        SnapshotTime: undefined,
        CompletedFlag: undefined,
        strategy: undefined
      }
    },

    /**
     * 初始化任务类型数据对象
     */
    initTaskTypeData () {
      return {
        used: null,
        xData: [],
        used2: null
      }
    },

    // 获取和处理数据方法

    /**
     * 处理多个任务的数据
     * @param {Array} tasksData - 多个任务的数据数组
     */
    processMultipleTasksData (tasksData) {
      console.log('开始处理多任务数据:', tasksData.length, '个任务')

      // 使用第一个任务作为主任务，折线图显示主任务的数据
      const mainTask = tasksData[0]

      // 设置基本任务信息（使用主任务的信息）
      this.initBaseTaskInfo(mainTask)

      // 使用主任务的数据设置折线图（与单任务模式保持一致）
      if (
        mainTask.CenterInfoToWebList != null &&
        mainTask.CenterInfoToWebList[0] &&
        mainTask.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      ) {
        this.total = mainTask.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      }

      const {
        historySubmitJob,
        historyCompleteJob,
        submitJob,
        completeJob,
        Time,
        city,
        submitJobCompare,
        pendingJobCompare,
        resouceCompare
      } = this.extractJobData(mainTask.CenterInfoToWebList)

      // 设置折线图数据（使用主任务的数据，与单任务模式一致）
      const xAxis = JSON.parse(JSON.stringify(Time))
      const yAxis = JSON.parse(JSON.stringify(submitJob))
      this.msg.SnapshotTime = Time[this.count]
      this.Data.xAxis = xAxis
      this.temp = JSON.parse(JSON.stringify(yAxis))
      this.Data.yAxis[this.count] = yAxis[this.count]

      this.runningTime = Time[this.count]

      // 初始化主任务的对比数据
      this.initMainCompareData(city, submitJobCompare, pendingJobCompare, resouceCompare)

      // 处理右侧多任务对比图表数据
      this.processMultipleTasksCompareData(tasksData)

      // 绘制图表
      this.drawLine()
      this.drawLine2()
      this.disable = false

      // 确保显示暂停/继续按钮
      this.showButtom = true

      // 启动动画定时器（使用主任务的数据）
      this.startMultiTaskAnimationTimer(Time, historySubmitJob, historyCompleteJob, city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used, xAxis, tasksData)
    },

    /**
     * 处理多任务对比数据
     * @param {Array} tasksData - 多个任务的数据数组
     */
    processMultipleTasksCompareData (tasksData) {
      console.log('processMultipleTasksCompareData 被调用，tasksData:', tasksData.length, '个任务')
      const compareData = MultiTaskService.getMultipleTasksCompareData(tasksData, this.count)
      console.log('MultiTaskService.getMultipleTasksCompareData 返回:', compareData)

      // 设置x轴数据（城市列表）
      this.taskData.xData = compareData.xData

      // 存储所有任务的数据
      this.taskData.multiTaskData = compareData.series.map(series => ({
        taskId: series.taskId,
        strategy: series.strategy,
        submitData: series.submitData,
        pendingData: series.pendingData,
        resourceData: series.resourceData
      }))

      console.log('设置后的 taskData.multiTaskData:', this.taskData.multiTaskData)

      // 兼容原有的双任务模式（保持向后兼容）
      if (compareData.series.length > 0) {
        const firstSeries = compareData.series[0]

        if (this.taskType === 1) {
          this.taskData.used = firstSeries.submitData
          if (compareData.series.length > 1) {
            this.taskData.used2 = compareData.series[1].submitData
          }
        } else if (this.taskType === 2) {
          this.taskData.used = firstSeries.pendingData
          if (compareData.series.length > 1) {
            this.taskData.used2 = compareData.series[1].pendingData
          }
        } else if (this.taskType === 3) {
          this.taskData.used = firstSeries.resourceData
          if (compareData.series.length > 1) {
            this.taskData.used2 = compareData.series[1].resourceData
          }
        }
      }
    },



    async getJobData () {
      const promises = []
      this.taskIdRes = null
      this.compareIdRes = null

      if (this.taskId !== 0) {
        const data1 = {
          id: this.taskId,
          resolution_n_hours: this.interval
        }
        promises.push(
          jobDetail(data1).then(res => {
            this.taskIdRes = res
          })
        )
      }

      if (this.compareId !== 0) {
        const data2 = {
          id: this.compareId,
          resolution_n_hours: this.interval
        }
        promises.push(
          jobDetail(data2).then(res => {
            this.compareIdRes = res
          })
        )
      }

      await Promise.all(promises)
      return {
        taskIdRes: this.taskIdRes,
        compareIdRes: this.compareIdRes
      }
    },

    processJobData (taskRes, compareRes) {
      this.prepareForProcessing()

      if (!taskRes) return

      // 处理主任务数据
      this.initBaseTaskInfo(taskRes)

      if (
        taskRes.CenterInfoToWebList != null &&
        taskRes.CenterInfoToWebList[0] &&
        taskRes.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      ) {
        this.total = taskRes.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      }

      const {
        historySubmitJob,
        historyCompleteJob,
        submitJob,
        completeJob,
        Time,
        city,
        submitJobCompare,
        pendingJobCompare,
        resouceCompare
      } =
        this.extractJobData(taskRes.CenterInfoToWebList)

      this.initMainCompareData(city, submitJobCompare, pendingJobCompare, resouceCompare)

      const xAxis = JSON.parse(JSON.stringify(Time))
      const yAxis = JSON.parse(JSON.stringify(submitJob))
      this.msg.SnapshotTime = Time[this.count]
      this.Data.xAxis = xAxis
      this.temp = JSON.parse(JSON.stringify(yAxis))
      this.Data.yAxis[this.count] = yAxis[this.count]

      this.runningTime = Time[this.count]
      this.updateTaskDataByType(city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used)

      // 处理对比任务数据
      if (compareRes) {
        this.processCompareData(compareRes, city)
      }

      this.finalizeProcessing()
      this.startAnimationTimer(Time, historySubmitJob, historyCompleteJob, city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used, xAxis)
    },

    /**
     * 准备处理数据
     */
    prepareForProcessing () {
      this.showStart = false
      clearInterval(this.timer)
      this.timer = null
      this.disable = true
      this.showButtom = true
      this.showPer = true
    },

    /**
     * 完成数据处理
     */
    finalizeProcessing () {
      this.drawLine()
      this.drawLine2()
      this.disable = false
    },

    processCompareData (compareRes, city) {
      if (!compareRes) return

      this.CenterInfoToWebList = compareRes.CenterInfoToWebList
      this.total2 = compareRes.CenterInfoToWebList[0].SnapshotInfoToWebList.length

      this.submitJobCompare = this.initTaskTypeData()
      this.pendingJobCompare = this.initTaskTypeData()
      this.resouceCompare = this.initTaskTypeData()

      const {
        city: compareCity,
        data1,
        data2,
        data3
      } = this.processCompareSnapshots(compareRes)

      this.submitJobCompare.xData = compareCity
      this.pendingJobCompare.xData = compareCity
      this.resouceCompare.xData = compareCity
      this.submitJobCompare.used2 = data1
      this.pendingJobCompare.used2 = data2
      this.resouceCompare.used2 = data3
      this.taskData.xData = city
    }
  },
  beforeDestroy () {
    // 清理资源
    if (this.myChart) {
      this.myChart.dispose()
    }
    if (this.myChart2) {
      this.myChart2.dispose()
    }
    window.removeEventListener('resize', this.resize)
    clearInterval(this.timer)
    this.timer = null
  }
}
</script>
<style lang="scss">
@import '@/styles/c2net';
</style>
